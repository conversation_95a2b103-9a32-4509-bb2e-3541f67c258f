<?php
if(empty($connection)){
  header('location:../../');
} else {
  // Akses untuk Admin dan Operator
  // Tidak ada proteksi khusus, semua level bisa akses

  include_once 'sw-mod/sw-panel.php';
echo'
  <div class="content-wrapper">';
switch(@$_GET['op']){
    default:
echo'
<section class="content-header">
  <h1>Data<small> Patroli</small></h1>
    <ol class="breadcrumb">
      <li><a href="./"><i class="fa fa-dashboard"></i> Beranda</a></li>
      <li class="active">Data Patroli</li>
    </ol>
</section>';
echo'
<section class="content">
  <div class="row">
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
      <div class="box box-solid">
        <div class="box-header with-border">
          <h3 class="box-title"><b>Data Patroli</b></h3>
          <div class="box-tools pull-right">';
          if($level_user==1 || $level_user==2){
            echo'
            <a href="'.$mod.'&op=add" class="btn btn-success btn-flat"><i class="fa fa-plus"></i> Tambah Baru</a>';}
          else{
            echo'<button type="button" class="btn btn-success btn-flat access-failed"><i class="fa fa-plus"></i> Tambah Baru</button>';
          }echo'
          </div>
        </div>
    <div class="box-body">
      <div class="table-responsive">
          <table id="sw-datatable" class="table table-bordered">
            <thead>
            <tr>
              <th style="width: 10px">No</th>
              <th>Tanggal</th>
              <th>Karyawan</th>
              <th>Lokasi</th>
              <th>Status</th>
              <th>Rating</th>
              <th>Dokumentasi</th>
              <th>Komentar</th>
              <th style="width:150px" class="text-center">Aksi</th>
            </tr>
            </thead>
            <tbody>
            </tbody>
            </table>
        </div>
      </div>
    </div>
  </div>
</section>';



break;

case 'add':
echo'
<section class="content-header">
  <h1>Tambah Data<small> Patroli</small></h1>
    <ol class="breadcrumb">
      <li><a href="./"><i class="fa fa-dashboard"></i> Beranda</a></li>
      <li><a href="./patroli"> Data Patroli</a></li>
      <li class="active">Tambah Patroli</li>
    </ol>
</section>';
echo'
<section class="content">
  <div class="row">
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
      <div class="box box-solid">
        <div class="box-header with-border">
          <h3 class="box-title"><b>Tambah Data Patroli</b></h3>
        </div>

        <div class="box-body">
            <form class="form-horizontal validate add-patroli">
              <div class="box-body">

                <div class="form-group">
                  <label class="col-sm-2 control-label">Karyawan <span class="text-red">*</span></label>
                  <div class="col-sm-6">
                   <select class="form-control select2" name="id_karyawan" required="">
                      <option value="">-- Pilih Karyawan --</option>';
                      $query_emp = "SELECT id, employees_name FROM employees ORDER BY employees_name ASC";
                      $result_emp = $connection->query($query_emp);
                      while($row_emp = $result_emp->fetch_assoc()) {
                      echo'<option value="'.$row_emp['id'].'">'.$row_emp['employees_name'].'</option>';
                      }echo'
                    </select>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-2 control-label">Lokasi <span class="text-red">*</span></label>
                  <div class="col-sm-6">
                   <select class="form-control select2" name="id_lokasi" required="">
                      <option value="">-- Pilih Lokasi --</option>';
                      $query_loc = "SELECT building_id, name FROM building ORDER BY name ASC";
                      $result_loc = $connection->query($query_loc);
                      while($row_loc = $result_loc->fetch_assoc()) {
                      echo'<option value="'.$row_loc['building_id'].'">'.$row_loc['name'].'</option>';
                      }echo'
                    </select>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-2 control-label">Tanggal <span class="text-red">*</span></label>
                  <div class="col-sm-6">
                    <input type="datetime-local" class="form-control" name="tanggal" required>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-2 control-label">Status <span class="text-red">*</span></label>
                  <div class="col-sm-6">
                   <select class="form-control" name="status" required="">
                      <option value="">-- Pilih Status --</option>
                      <option value="Normal">Normal</option>
                      <option value="Perlu Perhatian">Perlu Perhatian</option>
                      <option value="Darurat">Darurat</option>
                    </select>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-2 control-label">Rating <span class="text-red">*</span></label>
                  <div class="col-sm-6">
                   <select class="form-control" name="rating" required="">
                      <option value="">-- Pilih Rating --</option>
                      <option value="1">1 - Sangat Buruk</option>
                      <option value="2">2 - Buruk</option>
                      <option value="3">3 - Cukup</option>
                      <option value="4">4 - Baik</option>
                      <option value="5">5 - Sangat Baik</option>
                    </select>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-2 control-label">ID Ceklis <span class="text-red">*</span></label>
                  <div class="col-sm-6">
                    <input type="number" class="form-control" name="id_ceklis" required placeholder="Masukkan ID Ceklis">
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-2 control-label">Dokumentasi</label>
                  <div class="col-sm-6">
                    <input type="file" class="form-control" name="dokumentasi" accept="image/*,video/*,.pdf">
                    <small class="text-muted">Format yang diizinkan: JPG, PNG, MP4, PDF (Max: 5MB)</small>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-2 control-label">Komentar</label>
                  <div class="col-sm-6">
                    <textarea class="form-control" name="komentar" rows="3" placeholder="Masukkan komentar (opsional)"></textarea>
                  </div>
                </div>

              </div>
              <div class="box-footer">
                <a href="./patroli" class="btn btn-default">Batal</a>
                <button type="submit" class="btn btn-info pull-right">Simpan</button>
              </div>
            </form>
        </div>
      </div>
    </div>
  </div>
</section>';
break;

case 'edit':
echo'
<section class="content-header">
  <h1>Edit Data<small> Patroli</small></h1>
    <ol class="breadcrumb">
      <li><a href="./"><i class="fa fa-dashboard"></i> Beranda</a></li>
      <li><a href="./patroli"> Data Patroli</a></li>
      <li class="active">Edit Patroli</li>
    </ol>
</section>

<section class="content">
  <div class="row">
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
      <div class="box box-solid">
        <div class="box-header with-border">
          <h3 class="box-title"><b>Edit Data Patroli</b></h3>
        </div>

      <div class="box-body">';
      if(!empty($_GET['id'])){
      $id     =  mysqli_real_escape_string($connection, intval($_GET['id']));
      $query  ="SELECT * from patroli WHERE id_patroli='$id'";
      $result = $connection->query($query);
      if($result->num_rows > 0){
      $row  = $result->fetch_assoc();
      echo'
            <form class="form-horizontal validate edit-patroli">
              <div class="box-body">
                <input type="hidden" name="id_patroli" value="'.$row['id_patroli'].'">

                <div class="form-group">
                  <label class="col-sm-2 control-label">Karyawan <span class="text-red">*</span></label>
                  <div class="col-sm-6">
                   <select class="form-control select2" name="id_karyawan" required="">
                      <option value="">-- Pilih Karyawan --</option>';
                      $query_emp = "SELECT id, employees_name FROM employees ORDER BY employees_name ASC";
                      $result_emp = $connection->query($query_emp);
                      while($row_emp = $result_emp->fetch_assoc()) {
                        if($row_emp['id'] == $row['id_karyawan']){
                          echo'<option value="'.$row_emp['id'].'" selected>'.$row_emp['employees_name'].'</option>';
                        }else{
                          echo'<option value="'.$row_emp['id'].'">'.$row_emp['employees_name'].'</option>';
                        }
                      }echo'
                    </select>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-2 control-label">Lokasi <span class="text-red">*</span></label>
                  <div class="col-sm-6">
                   <select class="form-control select2" name="id_lokasi" required="">
                      <option value="">-- Pilih Lokasi --</option>';
                      $query_loc = "SELECT building_id, name FROM building ORDER BY name ASC";
                      $result_loc = $connection->query($query_loc);
                      while($row_loc = $result_loc->fetch_assoc()) {
                        if($row_loc['building_id'] == $row['id_lokasi']){
                          echo'<option value="'.$row_loc['building_id'].'" selected>'.$row_loc['name'].'</option>';
                        }else{
                          echo'<option value="'.$row_loc['building_id'].'">'.$row_loc['name'].'</option>';
                        }
                      }echo'
                    </select>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-2 control-label">Tanggal <span class="text-red">*</span></label>
                  <div class="col-sm-6">
                    <input type="datetime-local" class="form-control" name="tanggal" value="'.date('Y-m-d\TH:i', strtotime($row['tanggal'])).'" required>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-2 control-label">Status <span class="text-red">*</span></label>
                  <div class="col-sm-6">
                   <select class="form-control" name="status" required="">
                      <option value="">-- Pilih Status --</option>
                      <option value="Normal" '.($row['status']=='Normal'?'selected':'').'>Normal</option>
                      <option value="Perlu Perhatian" '.($row['status']=='Perlu Perhatian'?'selected':'').'>Perlu Perhatian</option>
                      <option value="Darurat" '.($row['status']=='Darurat'?'selected':'').'>Darurat</option>
                    </select>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-2 control-label">Rating <span class="text-red">*</span></label>
                  <div class="col-sm-6">
                   <select class="form-control" name="rating" required="">
                      <option value="">-- Pilih Rating --</option>
                      <option value="1" '.($row['rating']==1?'selected':'').'>1 - Sangat Buruk</option>
                      <option value="2" '.($row['rating']==2?'selected':'').'>2 - Buruk</option>
                      <option value="3" '.($row['rating']==3?'selected':'').'>3 - Cukup</option>
                      <option value="4" '.($row['rating']==4?'selected':'').'>4 - Baik</option>
                      <option value="5" '.($row['rating']==5?'selected':'').'>5 - Sangat Baik</option>
                    </select>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-2 control-label">ID Ceklis <span class="text-red">*</span></label>
                  <div class="col-sm-6">
                    <input type="number" class="form-control" name="id_ceklis" value="'.$row['id_ceklis'].'" required placeholder="Masukkan ID Ceklis">
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-2 control-label">Dokumentasi</label>
                  <div class="col-sm-6">
                    <input type="file" class="form-control" name="dokumentasi" accept="image/*,video/*,.pdf">
                    <small class="text-muted">Format yang diizinkan: JPG, PNG, MP4, PDF (Max: 5MB)</small>';
                    if($row['dokumentasi']){
                      echo'<br><small class="text-info">File saat ini: <a href="../sw-content/patroli/'.$row['dokumentasi'].'" target="_blank">'.$row['dokumentasi'].'</a></small>';
                    }
                    echo'
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-2 control-label">Komentar</label>
                  <div class="col-sm-6">
                    <textarea class="form-control" name="komentar" rows="3" placeholder="Masukkan komentar (opsional)">'.$row['komentar'].'</textarea>
                  </div>
                </div>

              </div>
              <div class="box-footer">
                <a href="./patroli" class="btn btn-default">Batal</a>
                <button type="submit" class="btn btn-info pull-right">Update</button>
              </div>
            </form>';
      }else{
        echo'<div class="alert alert-danger">Data tidak ditemukan!</div>';
      }
      }else{
        echo'<div class="alert alert-danger">ID tidak valid!</div>';
      }
      echo'
      </div>
    </div>
  </div>
</section>';
break;
}
echo'
  </div>';
include_once 'sw-mod/sw-footer.php';
}?>