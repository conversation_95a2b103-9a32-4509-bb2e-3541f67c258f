<?php
if(empty($connection)){
  header('location:../../');
} else {
  // Proteksi akses - hanya untuk Operator (Level 2)
  if($level_user != '2') {
    echo '<div class="content-wrapper">
            <section class="content">
              <div class="row">
                <div class="col-xs-12">
                  <div class="box box-danger">
                    <div class="box-header with-border">
                      <h3 class="box-title">A<PERSON><PERSON></h3>
                    </div>
                    <div class="box-body">
                      <div class="alert alert-danger">
                        <h4><i class="icon fa fa-ban"></i> Aks<PERSON>!</h4>
                        Halaman ini hanya dapat diakses oleh <strong>Operator</strong>.
                        Anda login sebagai <strong>Administrator</strong>.
                      </div>
                      <a href="./" class="btn btn-primary">
                        <i class="fa fa-arrow-left"></i> Kembali ke Dashboard
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </div>';
    include_once 'sw-mod/sw-footer.php';
    exit;
  }

  include_once 'sw-mod/sw-panel.php';
echo'
  <div class="content-wrapper">';
switch(@$_GET['op']){
    default:
echo'
<section class="content-header">
  <h1>Data<small> Patroli</small></h1>
    <ol class="breadcrumb">
      <li><a href="./"><i class="fa fa-dashboard"></i> Beranda</a></li>
      <li class="active">Data Patroli</li>
    </ol>
</section>';
echo'
<section class="content">
  <div class="row">
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
      <div class="box box-solid">
        <div class="box-header with-border">
          <h3 class="box-title"><b>Data Patroli</b></h3>
          <div class="box-tools pull-right">';
          if($level_user==2){
            echo'
            <a href="#modal-add-patroli" class="btn btn-success btn-flat" data-toggle="modal"><i class="fa fa-plus"></i> Tambah Baru</a>';}
          else{
            echo'<button type="button" class="btn btn-success btn-flat access-failed"><i class="fa fa-plus"></i> Tambah Baru</button>';
          }echo'
          </div>
        </div>
    <div class="box-body">
      <div class="table-responsive">
          <table id="sw-datatable" class="table table-bordered">
            <thead>
            <tr>
              <th style="width: 10px">No</th>
              <th>Tanggal</th>
              <th>Karyawan</th>
              <th>Lokasi</th>
              <th>Status</th>
              <th>Rating</th>
              <th>Dokumentasi</th>
              <th>Komentar</th>
              <th style="width:150px" class="text-center">Aksi</th>
            </tr>
            </thead>
            <tbody>
            </tbody>
            </table>
        </div>
      </div>
    </div>
  </div>
</section>';

// Modal Add Patroli
echo'
<div id="modal-add-patroli" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <form class="add-patroli" method="post" enctype="multipart/form-data">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
          <h4 class="modal-title">Tambah Data Patroli</h4>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>Karyawan <span class="text-red">*</span></label>
                <select name="id_karyawan" class="form-control select2" required>
                  <option value="">-- Pilih Karyawan --</option>';
                  $query_emp = "SELECT id, employees_name FROM employees ORDER BY employees_name ASC";
                  $result_emp = $connection->query($query_emp);
                  while($row_emp = $result_emp->fetch_assoc()){
                    echo '<option value="'.$row_emp['id'].'">'.$row_emp['employees_name'].'</option>';
                  }
                echo'
                </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>Lokasi <span class="text-red">*</span></label>
                <select name="id_lokasi" class="form-control select2" required>
                  <option value="">-- Pilih Lokasi --</option>';
                  $query_loc = "SELECT building_id, name FROM building ORDER BY name ASC";
                  $result_loc = $connection->query($query_loc);
                  while($row_loc = $result_loc->fetch_assoc()){
                    echo '<option value="'.$row_loc['building_id'].'">'.$row_loc['name'].'</option>';
                  }
                echo'
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>Tanggal <span class="text-red">*</span></label>
                <input type="datetime-local" name="tanggal" class="form-control" required>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>Status <span class="text-red">*</span></label>
                <select name="status" class="form-control" required>
                  <option value="">-- Pilih Status --</option>
                  <option value="Normal">Normal</option>
                  <option value="Perlu Perhatian">Perlu Perhatian</option>
                  <option value="Darurat">Darurat</option>
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>Rating <span class="text-red">*</span></label>
                <select name="rating" class="form-control" required>
                  <option value="">-- Pilih Rating --</option>
                  <option value="1">1 - Sangat Buruk</option>
                  <option value="2">2 - Buruk</option>
                  <option value="3">3 - Cukup</option>
                  <option value="4">4 - Baik</option>
                  <option value="5">5 - Sangat Baik</option>
                </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>ID Ceklis <span class="text-red">*</span></label>
                <input type="number" name="id_ceklis" class="form-control" required placeholder="Masukkan ID Ceklis">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <div class="form-group">
                <label>Dokumentasi</label>
                <input type="file" name="dokumentasi" class="form-control" accept="image/*,video/*,.pdf">
                <small class="text-muted">Format yang diizinkan: JPG, PNG, MP4, PDF (Max: 5MB)</small>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <div class="form-group">
                <label>Komentar</label>
                <textarea name="komentar" class="form-control" rows="3" placeholder="Masukkan komentar (opsional)"></textarea>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">Batal</button>
          <button type="submit" class="btn btn-primary">Simpan</button>
        </div>
      </form>
    </div>
  </div>
</div>';

// Modal Edit Patroli
echo'
<div id="modal-edit-patroli" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <form class="edit-patroli" method="post" enctype="multipart/form-data">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
          <h4 class="modal-title">Edit Data Patroli</h4>
        </div>
        <div class="modal-body">
          <input type="hidden" name="id_patroli" id="edit-id-patroli">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>Karyawan <span class="text-red">*</span></label>
                <select name="id_karyawan" id="edit-id-karyawan" class="form-control select2" required>
                  <option value="">-- Pilih Karyawan --</option>';
                  $query_emp2 = "SELECT id, employees_name FROM employees ORDER BY employees_name ASC";
                  $result_emp2 = $connection->query($query_emp2);
                  while($row_emp2 = $result_emp2->fetch_assoc()){
                    echo '<option value="'.$row_emp2['id'].'">'.$row_emp2['employees_name'].'</option>';
                  }
                echo'
                </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>Lokasi <span class="text-red">*</span></label>
                <select name="id_lokasi" id="edit-id-lokasi" class="form-control select2" required>
                  <option value="">-- Pilih Lokasi --</option>';
                  $query_loc2 = "SELECT building_id, name FROM building ORDER BY name ASC";
                  $result_loc2 = $connection->query($query_loc2);
                  while($row_loc2 = $result_loc2->fetch_assoc()){
                    echo '<option value="'.$row_loc2['building_id'].'">'.$row_loc2['name'].'</option>';
                  }
                echo'
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>Tanggal <span class="text-red">*</span></label>
                <input type="datetime-local" name="tanggal" id="edit-tanggal" class="form-control" required>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>Status <span class="text-red">*</span></label>
                <select name="status" id="edit-status" class="form-control" required>
                  <option value="">-- Pilih Status --</option>
                  <option value="Normal">Normal</option>
                  <option value="Perlu Perhatian">Perlu Perhatian</option>
                  <option value="Darurat">Darurat</option>
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>Rating <span class="text-red">*</span></label>
                <select name="rating" id="edit-rating" class="form-control" required>
                  <option value="">-- Pilih Rating --</option>
                  <option value="1">1 - Sangat Buruk</option>
                  <option value="2">2 - Buruk</option>
                  <option value="3">3 - Cukup</option>
                  <option value="4">4 - Baik</option>
                  <option value="5">5 - Sangat Baik</option>
                </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>ID Ceklis <span class="text-red">*</span></label>
                <input type="number" name="id_ceklis" id="edit-id-ceklis" class="form-control" required placeholder="Masukkan ID Ceklis">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <div class="form-group">
                <label>Dokumentasi</label>
                <input type="file" name="dokumentasi" class="form-control" accept="image/*,video/*,.pdf">
                <small class="text-muted">Format yang diizinkan: JPG, PNG, MP4, PDF (Max: 5MB)</small>
                <div id="current-documentation" class="mt-2"></div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <div class="form-group">
                <label>Komentar</label>
                <textarea name="komentar" id="edit-komentar" class="form-control" rows="3" placeholder="Masukkan komentar (opsional)"></textarea>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">Batal</button>
          <button type="submit" class="btn btn-primary">Update</button>
        </div>
      </form>
    </div>
  </div>
</div>';

break;
}
echo'
  </div>';
include_once 'sw-mod/sw-footer.php';
}?>