$(document).ready(function() {
    // Initialize DataTable
    var table = $('#sw-datatable').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "./sw-mod/patroli/sw-datatable.php",
            "type": "POST",
            "error": function(xhr, error, thrown) {
                console.log('DataTable Error:', error);
                alert('Error loading data. Please check console for details.');
            }
        },
        "columns": [
            { "data": 0, "orderable": false, "searchable": false }, // No
            { "data": 1, "type": "date" }, // Tanggal
            { "data": 2 }, // <PERSON><PERSON><PERSON>
            { "data": 3 }, // Lokasi
            { "data": 4 }, // Status
            { "data": 5, "orderable": false }, // Rating
            { "data": 6, "orderable": false, "searchable": false }, // Dokumentasi
            { "data": 7 }, // Komentar
            { "data": 8, "orderable": false, "searchable": false } // Aksi
        ],
        "order": [[1, "desc"]], // Sort by tanggal descending
        "iDisplayLength": 25,
        "aLengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]],
        "language": {
            "search": "Cari:",
            "lengthMenu": "Tampilkan _MENU_ data per halaman",
            "zeroRecords": "Data tidak ditemukan",
            "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            "infoEmpty": "Menampilkan 0 sampai 0 dari 0 data",
            "infoFiltered": "(disaring dari _MAX_ total data)",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            },
            "processing": "Memproses...",
            "loadingRecords": "Memuat data...",
            "emptyTable": "Tidak ada data patroli"
        }
    });

    // Initialize Select2
    $('.select2').select2({
        width: '100%'
    });

    // Loading function
    function loading(){
        $(".loading").show();
        $(".loading").delay(1500).fadeOut(500);
    }

    // Set default datetime to now
    $('#modal-add-patroli').on('shown.bs.modal', function () {
        var now = new Date();
        var year = now.getFullYear();
        var month = String(now.getMonth() + 1).padStart(2, '0');
        var day = String(now.getDate()).padStart(2, '0');
        var hours = String(now.getHours()).padStart(2, '0');
        var minutes = String(now.getMinutes()).padStart(2, '0');
        
        var datetime = year + '-' + month + '-' + day + 'T' + hours + ':' + minutes;
        $('input[name="tanggal"]').val(datetime);
    });

    // Handle Add Form Submit
    $('.add-patroli').on('submit', function(e) {
        e.preventDefault();
        
        // Validation
        var id_karyawan = $('select[name="id_karyawan"]').val();
        var id_lokasi = $('select[name="id_lokasi"]').val();
        var tanggal = $('input[name="tanggal"]').val();
        var status = $('select[name="status"]').val();
        var rating = $('select[name="rating"]').val();
        var id_ceklis = $('input[name="id_ceklis"]').val();

        if (!id_karyawan) {
            swal({
                title: 'Oops!', 
                text: 'Pilih karyawan terlebih dahulu!', 
                icon: 'warning', 
                timer: 2000
            });
            return false;
        }

        if (!id_lokasi) {
            swal({
                title: 'Oops!', 
                text: 'Pilih lokasi terlebih dahulu!', 
                icon: 'warning', 
                timer: 2000
            });
            return false;
        }

        if (!tanggal) {
            swal({
                title: 'Oops!', 
                text: 'Tanggal tidak boleh kosong!', 
                icon: 'warning', 
                timer: 2000
            });
            return false;
        }

        if (!status) {
            swal({
                title: 'Oops!', 
                text: 'Pilih status terlebih dahulu!', 
                icon: 'warning', 
                timer: 2000
            });
            return false;
        }

        if (!rating) {
            swal({
                title: 'Oops!', 
                text: 'Pilih rating terlebih dahulu!', 
                icon: 'warning', 
                timer: 2000
            });
            return false;
        }

        if (!id_ceklis) {
            swal({
                title: 'Oops!', 
                text: 'ID Ceklis tidak boleh kosong!', 
                icon: 'warning', 
                timer: 2000
            });
            return false;
        }

        loading();
        $.ajax({
            url: "./sw-mod/patroli/proses.php?action=add",
            type: "POST",
            data: new FormData(this),
            processData: false,
            contentType: false,
            cache: false,
            async: false,
            beforeSend: function() { 
                loading();
            },
            success: function(data) {
                if (data == 'success') {
                    swal({
                        title: 'Berhasil!', 
                        text: 'Data patroli berhasil ditambahkan!', 
                        icon: 'success', 
                        timer: 1500
                    });
                    $('#modal-add-patroli').modal('hide');
                    $('.add-patroli')[0].reset();
                    $('#sw-datatable').DataTable().ajax.reload();
                } else {
                    swal({
                        title: 'Oops!', 
                        text: data, 
                        icon: 'error', 
                        timer: 2000
                    });
                }
            },
            complete: function() {
                $(".loading").hide();
            },
            error: function() {
                swal({
                    title: 'Error!', 
                    text: 'Terjadi kesalahan sistem!', 
                    icon: 'error', 
                    timer: 1500
                });
            }
        });
    });

    // Handle Edit Form Submit
    $('.edit-patroli').on('submit', function(e) {
        e.preventDefault();
        
        loading();
        $.ajax({
            url: "./sw-mod/patroli/proses.php?action=edit",
            type: "POST",
            data: new FormData(this),
            processData: false,
            contentType: false,
            cache: false,
            async: false,
            beforeSend: function() { 
                loading();
            },
            success: function(data) {
                if (data == 'success') {
                    swal({
                        title: 'Berhasil!', 
                        text: 'Data patroli berhasil diupdate!', 
                        icon: 'success', 
                        timer: 1500
                    });
                    $('#modal-edit-patroli').modal('hide');
                    $('#sw-datatable').DataTable().ajax.reload();
                } else {
                    swal({
                        title: 'Oops!', 
                        text: data, 
                        icon: 'error', 
                        timer: 2000
                    });
                }
            },
            complete: function() {
                $(".loading").hide();
            },
            error: function() {
                swal({
                    title: 'Error!', 
                    text: 'Terjadi kesalahan sistem!', 
                    icon: 'error', 
                    timer: 1500
                });
            }
        });
    });

    // Reset form when modal is hidden
    $('#modal-add-patroli').on('hidden.bs.modal', function () {
        $('.add-patroli')[0].reset();
        $('.select2').val(null).trigger('change');
    });

    $('#modal-edit-patroli').on('hidden.bs.modal', function () {
        $('.edit-patroli')[0].reset();
        $('.select2').val(null).trigger('change');
        $('#current-documentation').html('');
    });
});

// Function to edit patroli
function editPatroli(id) {
    $.ajax({
        url: './sw-mod/patroli/proses.php?action=get_detail&id=' + id,
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            if (data.status == 'success') {
                var patroli = data.data;
                
                // Fill form fields
                $('#edit-id-patroli').val(patroli.id_patroli);
                $('#edit-id-karyawan').val(patroli.id_karyawan).trigger('change');
                $('#edit-id-lokasi').val(patroli.id_lokasi).trigger('change');
                $('#edit-tanggal').val(patroli.tanggal);
                $('#edit-status').val(patroli.status);
                $('#edit-rating').val(patroli.rating);
                $('#edit-id-ceklis').val(patroli.id_ceklis);
                $('#edit-komentar').val(patroli.komentar);
                
                // Show current documentation if exists
                if (patroli.dokumentasi) {
                    var fileExt = patroli.dokumentasi.split('.').pop().toLowerCase();
                    var currentDoc = '';
                    
                    if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExt)) {
                        currentDoc = '<p><strong>File saat ini:</strong> <a href="../sw-content/patroli/' + patroli.dokumentasi + '" target="_blank">Lihat Gambar</a></p>';
                    } else if (fileExt === 'pdf') {
                        currentDoc = '<p><strong>File saat ini:</strong> <a href="../sw-content/patroli/' + patroli.dokumentasi + '" target="_blank">Lihat PDF</a></p>';
                    } else if (['mp4', 'avi', 'mov'].includes(fileExt)) {
                        currentDoc = '<p><strong>File saat ini:</strong> <a href="../sw-content/patroli/' + patroli.dokumentasi + '" target="_blank">Lihat Video</a></p>';
                    } else {
                        currentDoc = '<p><strong>File saat ini:</strong> <a href="../sw-content/patroli/' + patroli.dokumentasi + '" target="_blank">Download File</a></p>';
                    }
                    
                    $('#current-documentation').html(currentDoc);
                }
                
                $('#modal-edit-patroli').modal('show');
            } else {
                swal({
                    title: 'Error!', 
                    text: data.message, 
                    icon: 'error', 
                    timer: 1500
                });
            }
        },
        error: function() {
            swal({
                title: 'Error!', 
                text: 'Terjadi kesalahan sistem!', 
                icon: 'error', 
                timer: 1500
            });
        }
    });
}

// Function to delete patroli
function deletePatroli(id) {
    swal({
        title: "Apakah Anda yakin?",
        text: "Data patroli akan dihapus permanen!",
        icon: "warning",
        buttons: true,
        dangerMode: true,
    }).then((willDelete) => {
        if (willDelete) {
            $.ajax({
                url: './sw-mod/patroli/proses.php?action=delete&id=' + id,
                type: 'GET',
                success: function(data) {
                    if (data == 'success') {
                        swal({
                            title: 'Berhasil!', 
                            text: 'Data patroli berhasil dihapus!', 
                            icon: 'success', 
                            timer: 1500
                        });
                        $('#sw-datatable').DataTable().ajax.reload();
                    } else {
                        swal({
                            title: 'Oops!', 
                            text: data, 
                            icon: 'error', 
                            timer: 1500
                        });
                    }
                },
                error: function() {
                    swal({
                        title: 'Error!', 
                        text: 'Terjadi kesalahan sistem!', 
                        icon: 'error', 
                        timer: 1500
                    });
                }
            });
        }
    });
}

// Function to view documentation
function viewDokumentasi(filename) {
    if (!filename) {
        swal({
            title: 'Info', 
            text: 'Tidak ada dokumentasi untuk data ini', 
            icon: 'info', 
            timer: 1500
        });
        return;
    }
    
    var fileExt = filename.split('.').pop().toLowerCase();
    var fileUrl = '../sw-content/patroli/' + filename;
    
    if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExt)) {
        // Show image in modal
        swal({
            title: 'Dokumentasi',
            content: {
                element: "img",
                attributes: {
                    src: fileUrl,
                    style: "max-width: 100%; height: auto;"
                }
            }
        });
    } else {
        // Open file in new tab
        window.open(fileUrl, '_blank');
    }
}
