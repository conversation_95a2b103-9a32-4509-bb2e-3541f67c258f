<?php
session_start();
if(empty($_SESSION['SESSION_USER']) && empty($_SESSION['SESSION_ID'])){
    header('location:../../login/');
 exit;}
else {
require_once'../../../sw-library/sw-config.php';
require_once'../../login/login_session.php';
include('../../../sw-library/sw-function.php');

// Proteksi akses - hanya untuk Operator (Level 2)
if($level_user != '2') {
    echo 'Akses ditolak';
    exit;
}

$max_size = 5000000; // 5MB
$allowed_extensions = array('jpg', 'jpeg', 'png', 'gif', 'mp4', 'avi', 'mov', 'pdf');

switch (@$_GET['action']){

case 'add':
  $error = array();
  
  if (empty($_POST['id_karyawan'])) {
      $error[] = 'Karyawan tidak boleh kosong';
    } else {
      $id_karyawan = anti_injection($_POST['id_karyawan']);
  }

  if (empty($_POST['id_lokasi'])) {
      $error[] = 'Lokasi tidak boleh kosong';
    } else {
      $id_lokasi = anti_injection($_POST['id_lokasi']);
  }

  if (empty($_POST['tanggal'])) {
      $error[] = 'Tanggal tidak boleh kosong';
    } else {
      $tanggal = date('Y-m-d H:i:s', strtotime($_POST['tanggal']));
  }

  if (empty($_POST['status'])) {
      $error[] = 'Status tidak boleh kosong';
    } else {
      $status = anti_injection($_POST['status']);
  }

  if (empty($_POST['rating'])) {
      $error[] = 'Rating tidak boleh kosong';
    } else {
      $rating = intval($_POST['rating']);
      if ($rating < 1 || $rating > 5) {
          $error[] = 'Rating harus antara 1-5';
      }
  }

  if (empty($_POST['id_ceklis'])) {
      $error[] = 'ID Ceklis tidak boleh kosong';
    } else {
      $id_ceklis = intval($_POST['id_ceklis']);
  }

  $komentar = isset($_POST['komentar']) ? anti_injection($_POST['komentar']) : null;
  
  // Handle file upload
  $dokumentasi = null;
  if (isset($_FILES['dokumentasi']) && $_FILES['dokumentasi']['error'] == 0) {
      $file_name = $_FILES['dokumentasi']['name'];
      $file_size = $_FILES['dokumentasi']['size'];
      $file_tmp = $_FILES['dokumentasi']['tmp_name'];
      $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
      
      if (!in_array($file_ext, $allowed_extensions)) {
          $error[] = 'Format file tidak diizinkan. Gunakan: ' . implode(', ', $allowed_extensions);
      }
      
      if ($file_size > $max_size) {
          $error[] = 'Ukuran file terlalu besar. Maksimal 5MB';
      }
      
      if (empty($error)) {
          $new_filename = 'patroli_' . time() . '_' . rand(1000, 9999) . '.' . $file_ext;
          $upload_path = '../../../sw-content/patroli/' . $new_filename;
          
          // Create directory if not exists
          if (!file_exists('../../../sw-content/patroli/')) {
              mkdir('../../../sw-content/patroli/', 0777, true);
          }
          
          if (move_uploaded_file($file_tmp, $upload_path)) {
              $dokumentasi = $new_filename;
          } else {
              $error[] = 'Gagal mengupload file';
          }
      }
  }

  if (empty($error)) {
      $query = "INSERT INTO patroli (id_karyawan, id_lokasi, tanggal, status, rating, id_ceklis, komentar, dokumentasi) 
                VALUES ('$id_karyawan', '$id_lokasi', '$tanggal', '$status', '$rating', '$id_ceklis', '$komentar', '$dokumentasi')";
      
      if ($connection->query($query) === TRUE) {
          echo 'success';
      } else {
          echo 'Gagal menyimpan data: ' . $connection->error;
      }
  } else {
      echo implode(', ', $error);
  }
break;

case 'edit':
  $error = array();
  
  if (empty($_POST['id_patroli'])) {
      $error[] = 'ID Patroli tidak boleh kosong';
    } else {
      $id_patroli = intval($_POST['id_patroli']);
  }
  
  if (empty($_POST['id_karyawan'])) {
      $error[] = 'Karyawan tidak boleh kosong';
    } else {
      $id_karyawan = anti_injection($_POST['id_karyawan']);
  }

  if (empty($_POST['id_lokasi'])) {
      $error[] = 'Lokasi tidak boleh kosong';
    } else {
      $id_lokasi = anti_injection($_POST['id_lokasi']);
  }

  if (empty($_POST['tanggal'])) {
      $error[] = 'Tanggal tidak boleh kosong';
    } else {
      $tanggal = date('Y-m-d H:i:s', strtotime($_POST['tanggal']));
  }

  if (empty($_POST['status'])) {
      $error[] = 'Status tidak boleh kosong';
    } else {
      $status = anti_injection($_POST['status']);
  }

  if (empty($_POST['rating'])) {
      $error[] = 'Rating tidak boleh kosong';
    } else {
      $rating = intval($_POST['rating']);
      if ($rating < 1 || $rating > 5) {
          $error[] = 'Rating harus antara 1-5';
      }
  }

  if (empty($_POST['id_ceklis'])) {
      $error[] = 'ID Ceklis tidak boleh kosong';
    } else {
      $id_ceklis = intval($_POST['id_ceklis']);
  }

  $komentar = isset($_POST['komentar']) ? anti_injection($_POST['komentar']) : null;
  
  // Get current data
  $current_query = "SELECT dokumentasi FROM patroli WHERE id_patroli = '$id_patroli'";
  $current_result = $connection->query($current_query);
  $current_data = $current_result->fetch_assoc();
  $dokumentasi = $current_data['dokumentasi'];
  
  // Handle file upload
  if (isset($_FILES['dokumentasi']) && $_FILES['dokumentasi']['error'] == 0) {
      $file_name = $_FILES['dokumentasi']['name'];
      $file_size = $_FILES['dokumentasi']['size'];
      $file_tmp = $_FILES['dokumentasi']['tmp_name'];
      $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
      
      if (!in_array($file_ext, $allowed_extensions)) {
          $error[] = 'Format file tidak diizinkan. Gunakan: ' . implode(', ', $allowed_extensions);
      }
      
      if ($file_size > $max_size) {
          $error[] = 'Ukuran file terlalu besar. Maksimal 5MB';
      }
      
      if (empty($error)) {
          $new_filename = 'patroli_' . time() . '_' . rand(1000, 9999) . '.' . $file_ext;
          $upload_path = '../../../sw-content/patroli/' . $new_filename;
          
          // Create directory if not exists
          if (!file_exists('../../../sw-content/patroli/')) {
              mkdir('../../../sw-content/patroli/', 0777, true);
          }
          
          if (move_uploaded_file($file_tmp, $upload_path)) {
              // Delete old file
              if ($dokumentasi && file_exists('../../../sw-content/patroli/' . $dokumentasi)) {
                  unlink('../../../sw-content/patroli/' . $dokumentasi);
              }
              $dokumentasi = $new_filename;
          } else {
              $error[] = 'Gagal mengupload file';
          }
      }
  }

  if (empty($error)) {
      $query = "UPDATE patroli SET 
                id_karyawan = '$id_karyawan',
                id_lokasi = '$id_lokasi',
                tanggal = '$tanggal',
                status = '$status',
                rating = '$rating',
                id_ceklis = '$id_ceklis',
                komentar = '$komentar',
                dokumentasi = '$dokumentasi'
                WHERE id_patroli = '$id_patroli'";
      
      if ($connection->query($query) === TRUE) {
          echo 'success';
      } else {
          echo 'Gagal mengupdate data: ' . $connection->error;
      }
  } else {
      echo implode(', ', $error);
  }
break;

case 'delete':
  if (empty($_GET['id'])) {
      echo 'ID tidak boleh kosong';
  } else {
      $id = intval($_GET['id']);
      
      // Get file info before delete
      $file_query = "SELECT dokumentasi FROM patroli WHERE id_patroli = '$id'";
      $file_result = $connection->query($file_query);
      $file_data = $file_result->fetch_assoc();
      
      $query = "DELETE FROM patroli WHERE id_patroli = '$id'";
      
      if ($connection->query($query) === TRUE) {
          // Delete file if exists
          if ($file_data['dokumentasi'] && file_exists('../../../sw-content/patroli/' . $file_data['dokumentasi'])) {
              unlink('../../../sw-content/patroli/' . $file_data['dokumentasi']);
          }
          echo 'success';
      } else {
          echo 'Gagal menghapus data: ' . $connection->error;
      }
  }
break;

case 'get_detail':
  if (empty($_GET['id'])) {
      echo json_encode(array('status' => 'error', 'message' => 'ID tidak boleh kosong'));
  } else {
      $id = intval($_GET['id']);
      
      $query = "SELECT * FROM patroli WHERE id_patroli = '$id'";
      $result = $connection->query($query);
      
      if ($result->num_rows > 0) {
          $data = $result->fetch_assoc();
          // Format tanggal untuk input datetime-local
          $data['tanggal'] = date('Y-m-d\TH:i', strtotime($data['tanggal']));
          echo json_encode(array('status' => 'success', 'data' => $data));
      } else {
          echo json_encode(array('status' => 'error', 'message' => 'Data tidak ditemukan'));
      }
  }
break;

default:
    echo 'Action tidak valid';
break;
}
}
?>
