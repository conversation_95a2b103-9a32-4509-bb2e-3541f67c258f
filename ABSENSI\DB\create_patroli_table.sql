-- SQL untuk membuat tabel patroli
-- Jalankan script ini di database absensi_sigap
-- Tabel patroli dapat diakses oleh Admin dan Operator

-- Membuat tabel patroli
CREATE TABLE `patroli` (
  `id_patroli` int(11) NOT NULL AUTO_INCREMENT,
  `dokumentasi` varchar(255) DEFAULT NULL,
  `tanggal` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(50) NOT NULL,
  `komentar` text DEFAULT NULL,
  `rating` int(1) NOT NULL DEFAULT 1,
  `id_karyawan` int(11) NOT NULL,
  `id_lokasi` int(11) NOT NULL,
  `id_ceklis` int(11) NOT NULL,
  PRIMARY KEY (`id_patroli`),
  KEY `id_karyawan` (`id_karyawan`),
  KEY `id_lokasi` (`id_lokasi`),
  CONSTRAINT `patroli_ibfk_1` FOREIGN KEY (`id_karyawan`) REFERENCES `employees` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `patroli_ibfk_2` FOREIGN KEY (`id_lokasi`) REFERENCES `building` (`building_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Menambahkan beberapa data contoh (opsional)
-- INSERT INTO `patroli` (`dokumentasi`, `tanggal`, `status`, `komentar`, `rating`, `id_karyawan`, `id_lokasi`, `id_ceklis`) VALUES
-- (NULL, '2024-07-09 10:00:00', 'Normal', 'Patroli rutin pagi hari', 5, 1, 1, 1),
-- (NULL, '2024-07-09 14:00:00', 'Perlu Perhatian', 'Ada lampu yang mati di koridor', 3, 1, 1, 2),
-- (NULL, '2024-07-09 18:00:00', 'Normal', 'Patroli sore hari berjalan lancar', 4, 1, 1, 3);
