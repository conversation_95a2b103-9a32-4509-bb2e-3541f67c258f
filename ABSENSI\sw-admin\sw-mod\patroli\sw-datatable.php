<?php session_start();
if(empty($_SESSION['SESSION_USER']) && empty($_SESSION['SESSION_ID'])){
    header('location:../../login/');
 exit;}
else{
require_once'../../../sw-library/sw-config.php';
require_once'../../login/login_session.php';
include('../../../sw-library/sw-function.php');

// Akses untuk Admin dan Operator
// Tidak ada proteksi khusus, semua level bisa akses

// Server-side processing untuk DataTable
$table = 'patroli';
$primaryKey = 'id_patroli';

// Kolom yang akan ditampilkan
$columns = array(
    array('db' => 'id_patroli', 'dt' => 0),
    array('db' => 'tanggal', 'dt' => 1),
    array('db' => 'id_karyawan', 'dt' => 2),
    array('db' => 'id_lokasi', 'dt' => 3),
    array('db' => 'status', 'dt' => 4),
    array('db' => 'rating', 'dt' => 5),
    array('db' => 'dokumentasi', 'dt' => 6),
    array('db' => 'komentar', 'dt' => 7),
    array('db' => 'id_patroli', 'dt' => 8)
);

// Database connection details
$sql_details = array(
    'user' => DB_USER,
    'pass' => DB_PASSWD,
    'db'   => DB_NAME,
    'host' => DB_HOST
);

// Limit
$limit = '';
if (isset($_POST['start']) && $_POST['length'] != -1) {
    $limit = "LIMIT " . intval($_POST['start']) . ", " . intval($_POST['length']);
}

// Order
$order = '';
if (isset($_POST['order'])) {
    $orderBy = array();
    for ($i = 0; $i < count($_POST['order']); $i++) {
        $columnIdx = intval($_POST['order'][$i]['column']);
        $requestColumn = $_POST['columns'][$columnIdx];
        
        $columnName = $requestColumn['data'];
        if ($columnName == 1) $columnName = 'tanggal';
        elseif ($columnName == 2) $columnName = 'id_karyawan';
        elseif ($columnName == 3) $columnName = 'id_lokasi';
        elseif ($columnName == 4) $columnName = 'status';
        
        if ($requestColumn['orderable'] == 'true') {
            $dir = $_POST['order'][$i]['dir'] === 'asc' ? 'ASC' : 'DESC';
            $orderBy[] = $columnName . ' ' . $dir;
        }
    }
    if (count($orderBy)) {
        $order = 'ORDER BY ' . implode(', ', $orderBy);
    }
}

if (!$order) {
    $order = 'ORDER BY tanggal DESC';
}

// Search
$where = '';
if (isset($_POST['search']) && $_POST['search']['value'] != '') {
    $searchValue = mysqli_real_escape_string($connection, $_POST['search']['value']);
    $where = "WHERE (e.employees_name LIKE '%$searchValue%' OR 
                     b.name LIKE '%$searchValue%' OR 
                     p.status LIKE '%$searchValue%' OR 
                     p.komentar LIKE '%$searchValue%')";
}

// Main query with JOIN
$query = "SELECT p.id_patroli, p.tanggal, p.status, p.rating, p.dokumentasi, p.komentar, p.id_ceklis,
                 e.employees_name, b.name as lokasi_name
          FROM patroli p
          LEFT JOIN employees e ON p.id_karyawan = e.id
          LEFT JOIN building b ON p.id_lokasi = b.building_id
          $where
          $order
          $limit";

$result = $connection->query($query);

// Count total records
$totalQuery = "SELECT COUNT(*) as total FROM patroli p
               LEFT JOIN employees e ON p.id_karyawan = e.id
               LEFT JOIN building b ON p.id_lokasi = b.building_id";
$totalResult = $connection->query($totalQuery);
$totalRecords = $totalResult->fetch_assoc()['total'];

// Count filtered records
$filteredQuery = "SELECT COUNT(*) as total FROM patroli p
                  LEFT JOIN employees e ON p.id_karyawan = e.id
                  LEFT JOIN building b ON p.id_lokasi = b.building_id
                  $where";
$filteredResult = $connection->query($filteredQuery);
$filteredRecords = $filteredResult->fetch_assoc()['total'];

$data = array();
$no = isset($_POST['start']) ? intval($_POST['start']) + 1 : 1;

while ($row = $result->fetch_assoc()) {
    // Format tanggal
    $tanggal = date('d/m/Y H:i', strtotime($row['tanggal']));
    
    // Format rating dengan bintang
    $rating = '';
    for ($i = 1; $i <= 5; $i++) {
        if ($i <= $row['rating']) {
            $rating .= '<i class="fa fa-star text-yellow"></i>';
        } else {
            $rating .= '<i class="fa fa-star-o text-muted"></i>';
        }
    }
    
    // Format dokumentasi
    $dokumentasi = '';
    if ($row['dokumentasi']) {
        $fileExt = strtolower(pathinfo($row['dokumentasi'], PATHINFO_EXTENSION));
        if (in_array($fileExt, ['jpg', 'jpeg', 'png', 'gif'])) {
            $dokumentasi = '<button class="btn btn-info btn-xs" onclick="viewDokumentasi(\''.$row['dokumentasi'].'\')">
                            <i class="fa fa-image"></i> Lihat
                           </button>';
        } elseif ($fileExt == 'pdf') {
            $dokumentasi = '<button class="btn btn-warning btn-xs" onclick="viewDokumentasi(\''.$row['dokumentasi'].'\')">
                            <i class="fa fa-file-pdf-o"></i> PDF
                           </button>';
        } elseif (in_array($fileExt, ['mp4', 'avi', 'mov'])) {
            $dokumentasi = '<button class="btn btn-success btn-xs" onclick="viewDokumentasi(\''.$row['dokumentasi'].'\')">
                            <i class="fa fa-video-camera"></i> Video
                           </button>';
        } else {
            $dokumentasi = '<button class="btn btn-default btn-xs" onclick="viewDokumentasi(\''.$row['dokumentasi'].'\')">
                            <i class="fa fa-file"></i> File
                           </button>';
        }
    } else {
        $dokumentasi = '<span class="text-muted">-</span>';
    }
    
    // Format komentar
    $komentar = $row['komentar'] ? substr(strip_tags($row['komentar']), 0, 50) . '...' : '-';
    
    // Tombol aksi berdasarkan level user
    if($level_user==1 || $level_user==2){
        $aksi = '<div class="btn-group">
                   <a href="patroli&op=edit&id='.$row['id_patroli'].'" class="btn btn-warning btn-sm enable-tooltip" title="Edit">
                     <i class="fa fa-pencil-square-o"></i>
                   </a>
                   <button class="btn btn-sm btn-danger delete" data-id="'.$row['id_patroli'].'" title="Hapus">
                     <i class="fa fa-trash-o"></i>
                   </button>
                 </div>';
    } else {
        $aksi = '<div class="btn-group">
                   <button type="button" class="btn btn-warning btn-sm access-failed enable-tooltip" title="Edit">
                     <i class="fa fa-pencil-square-o"></i>
                   </button>
                   <button type="button" class="btn btn-sm btn-danger access-failed" title="Hapus">
                     <i class="fa fa-trash-o"></i>
                   </button>
                 </div>';
    }
    
    $data[] = array(
        $no,
        $tanggal,
        $row['employees_name'],
        $row['lokasi_name'],
        '<span class="label label-'.($row['status'] == 'Normal' ? 'success' : ($row['status'] == 'Darurat' ? 'danger' : 'warning')).'">'.$row['status'].'</span>',
        $rating,
        $dokumentasi,
        $komentar,
        $aksi
    );
    $no++;
}

$response = array(
    "draw" => isset($_POST['draw']) ? intval($_POST['draw']) : 0,
    "recordsTotal" => intval($totalRecords),
    "recordsFiltered" => intval($filteredRecords),
    "data" => $data
);

echo json_encode($response);
}
?>
